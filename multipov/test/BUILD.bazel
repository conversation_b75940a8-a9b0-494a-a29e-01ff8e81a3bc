load("@rules_cc//cc:defs.bzl", "cc_test")

cc_test(
    name = "hello_test",
    srcs = ["hello_test.cpp"],
    deps = [
        "//:multipov_lib",
        "@googletest//:gtest_main",
    ],
    includes = ["../src"],
)

cc_test(
    name = "sliding_window_buffer_test",
    srcs = ["sliding_window_buffer_test.cpp"],
    deps = [
        "//:multipov_lib",
        "@googletest//:gtest_main",
    ],
    includes = ["../src"],
)
