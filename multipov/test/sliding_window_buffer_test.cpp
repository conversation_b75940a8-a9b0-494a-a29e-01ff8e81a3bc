#include <gtest/gtest.h>
#include "lib/sliding_window_buffer.h"
#include <string>

class SlidingWindowBufferTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup code if needed
    }
};

// Test basic construction and properties
TEST_F(SlidingWindowBufferTest, Construction) {
    SlidingWindowBuffer<int, 5> buffer;

    EXPECT_EQ(buffer.capacity(), 5);
    EXPECT_EQ(buffer.size(), 0);
    EXPECT_TRUE(buffer.empty());
    EXPECT_FALSE(buffer.full());
}

// Note: Invalid capacity is now caught at compile time with static_assert
TEST_F(SlidingWindowBufferTest, CompileTimeCapacity) {
    SlidingWindowBuffer<int, 1> buffer;
    EXPECT_EQ(buffer.capacity(), 1);
}

// Test adding elements when buffer is not full
TEST_F(SlidingWindowBufferTest, PushWhenNotFull) {
    SlidingWindowBuffer<int, 3> buffer;
    
    buffer.push(10);
    EXPECT_EQ(buffer.size(), 1);
    EXPECT_EQ(buffer[0], 10);
    EXPECT_EQ(buffer.front(), 10);
    EXPECT_EQ(buffer.back(), 10);
    
    buffer.push(20);
    EXPECT_EQ(buffer.size(), 2);
    EXPECT_EQ(buffer[0], 10);
    EXPECT_EQ(buffer[1], 20);
    EXPECT_EQ(buffer.front(), 10);
    EXPECT_EQ(buffer.back(), 20);
    
    buffer.push(30);
    EXPECT_EQ(buffer.size(), 3);
    EXPECT_EQ(buffer[0], 10);
    EXPECT_EQ(buffer[1], 20);
    EXPECT_EQ(buffer[2], 30);
    EXPECT_TRUE(buffer.full());
}

// Test sliding window behavior when buffer is full
TEST_F(SlidingWindowBufferTest, PushWhenFull) {
    SlidingWindowBuffer<int, 3> buffer;
    
    // Fill the buffer
    buffer.push(10);
    buffer.push(20);
    buffer.push(30);
    
    // Add one more - should slide the window
    buffer.push(40);
    EXPECT_EQ(buffer.size(), 3);
    EXPECT_EQ(buffer[0], 20);  // 10 was removed
    EXPECT_EQ(buffer[1], 30);
    EXPECT_EQ(buffer[2], 40);
    EXPECT_EQ(buffer.front(), 20);
    EXPECT_EQ(buffer.back(), 40);
    
    // Add another
    buffer.push(50);
    EXPECT_EQ(buffer.size(), 3);
    EXPECT_EQ(buffer[0], 30);  // 20 was removed
    EXPECT_EQ(buffer[1], 40);
    EXPECT_EQ(buffer[2], 50);
}

// Test with different types
TEST_F(SlidingWindowBufferTest, StringType) {
    SlidingWindowBuffer<std::string, 2> buffer;
    
    buffer.push("hello");
    buffer.push("world");
    
    EXPECT_EQ(buffer[0], "hello");
    EXPECT_EQ(buffer[1], "world");
    
    buffer.push("test");
    EXPECT_EQ(buffer[0], "world");
    EXPECT_EQ(buffer[1], "test");
}

// Test bounds checking
TEST_F(SlidingWindowBufferTest, BoundsChecking) {
    SlidingWindowBuffer<int, 3> buffer;
    
    // Empty buffer
    EXPECT_THROW(buffer.at(0), std::out_of_range);
    EXPECT_THROW(buffer.front(), std::runtime_error);
    EXPECT_THROW(buffer.back(), std::runtime_error);
    
    buffer.push(10);
    EXPECT_NO_THROW(buffer.at(0));
    EXPECT_THROW(buffer.at(1), std::out_of_range);
}

// Test clear functionality
TEST_F(SlidingWindowBufferTest, Clear) {
    SlidingWindowBuffer<int, 3> buffer;
    
    buffer.push(10);
    buffer.push(20);
    buffer.push(30);
    
    EXPECT_EQ(buffer.size(), 3);
    EXPECT_FALSE(buffer.empty());
    
    buffer.clear();
    
    EXPECT_EQ(buffer.size(), 0);
    EXPECT_TRUE(buffer.empty());
    EXPECT_FALSE(buffer.full());
}

// Test to_vector functionality
TEST_F(SlidingWindowBufferTest, ToVector) {
    SlidingWindowBuffer<int, 3> buffer;
    
    buffer.push(10);
    buffer.push(20);
    buffer.push(30);
    buffer.push(40);  // Should slide: [20, 30, 40]
    
    auto vec = buffer.to_vector();
    std::vector<int> expected = {20, 30, 40};
    
    EXPECT_EQ(vec, expected);
}

// Test modifying elements
TEST_F(SlidingWindowBufferTest, ModifyElements) {
    SlidingWindowBuffer<int, 3> buffer;
    
    buffer.push(10);
    buffer.push(20);
    buffer.push(30);
    
    buffer[1] = 99;
    EXPECT_EQ(buffer[1], 99);
    EXPECT_EQ(buffer.at(1), 99);
}
