//#include "matcher.h"
//#include "SlidingWindows.h"
#include "LockFreeQueue.h"
#include <chrono>
#include <thread>
#include <iostream>

int main() {
    LockFreeQueue<int> queue;
    // Example usage
    queue.enqueue(1);
    queue.enqueue(2);
    queue.enqueue(3);
    int value;
    while (queue.dequeue(value)) {
        std::cout << "Dequeued: " << value << std::endl;
    }
    return 0;
}

// int main() {
//     Aggregator aggregator;

//     // Simulating adding entries with timestamps
//     auto now = std::chrono::system_clock::now();
//     aggregator.addEntry(Entry(10, 100.0), now.time_since_epoch().count()); // Current time
//     aggregator.addEntry(Entry(5, 200.0), now.time_since_epoch().count());  // Current time
//     std::this_thread::sleep_for(std::chrono::seconds(1)); // Wait for 1 second
//     aggregator.addEntry(Entry(8, 150.0), std::chrono::system_clock::now().time_since_epoch().count()); // Next second
//     std::this_thread::sleep_for(std::chrono::seconds(1)); // Wait for 1 second
//     aggregator.addEntry(Entry(12, 120.0), std::chrono::system_clock::now().time_since_epoch().count()); // Next second

//     // Print aggregated results
//     aggregator.printAggregates();

//     return 0;
// }

// int main()
// {   
//     std::cout << "hello\n";
//     {
//         MatchingEngine<int> e;
//         e.Add({"abc", "def"}, 100);
//         auto t = e.Find({"abc", "def"});
//         if (!t.empty())
//             std::cout << t[0] << " Found\n ";
//         else
//             std::cout << "not found\n";
//     }

//     Test<int> t(100);
    
//     t.Print(100);
//     t.Print(100.0);
//     t.Print("xxx");

    

//     Test<double> u(200.0);
//     u.Print(200.0);

//     Test<std::string> v("hello");

//     v.Print(100);
//     v.Print(100.0);
//     v.Print("xxx");

// }