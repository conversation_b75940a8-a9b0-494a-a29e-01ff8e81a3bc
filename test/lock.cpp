#include <atomic>
#include <memory>
#include <iostream>

template<typename T>
class LockFreeQueue {
private:
    struct Node {
        T data;
        std::atomic<Node*> next;
        Node(const T& value) : data(value), next(nullptr) {}
    };

    std::atomic<Node*> head;
    std::atomic<Node*> tail;

public:
    LockFreeQueue() {
        Node* dummy = new Node(T());
        head.store(dummy);
        tail.store(dummy);
    }

    ~LockFreeQueue() {
        Node* current = head.load();
        while (current) {
            Node* next = current->next.load();
            delete current;
            current = next;
        }
    }

    void enqueue(const T& value)
    {
        Node* newNode = new Node(value);
        Node* oldTail = tail.load();
        Node* nullNode = nullptr; 
        while (!oldTail->next.compare_exchange_strong(nullNode,newNode))
        {
            tail.compare_exchange_strong(oldTail, oldTail->next.load());
        }
        tail.compare_exchange_strong(oldTail, newNode);
    }

    bool dequeue(T& result)
    {
        Node* oldHead = head.load();
        Node* newHead = oldHead->next.load();
        while (!head.compare_exchange_strong(oldHead, newHead))
        {
            oldHead = head.load();
            newHead = oldHead->next.load();
        }
        if (!newHead)
        {
            return false;
        }
        
        result = newHead->data;
        delete oldHead;
        return true;
        
    }

};

int main() {
    LockFreeQueue<int> queue;
    // Example usage
    queue.enqueue(18);
    queue.enqueue(2);
    queue.enqueue(3);
    queue.enqueue(5);
    int value;
    while (queue.dequeue(value)) {
        std::cout << "Dequeued: " << value << std::endl;
    }
    return 0;
  }