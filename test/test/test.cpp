#include <gtest/gtest.h>
#include "../lib/matcher.h"

// Demonstrate some basic assertions.
// TEST(HelloTest, BasicAssertions) {
//   // Expect two strings not to be equal.
//   EXPECT_STRNE("hello", "world");
//   // Expect equality.
//   EXPECT_EQ(7 * 6, 42);
// }

TEST(MatcherTest, singleMatch)
{
  MatchingEngine<int> subject;
  subject.Add({"ab", "cd"}, 100);
  subject.Add({"*", "cd"}, 200);
  subject.Add({"ab", "*"}, 300);
  {
    std::vector<std::string> pattern1{"ab", "cd"};
    auto t = subject.Find(pattern1);
    EXPECT_EQ(*t[0].get(), 100);
  }
  {
    std::vector<std::string> pattern1{"abb", "cd"};
    auto t = subject.Find(pattern1);
    EXPECT_EQ(*t[0].get(), 200);
  }
  {
    std::vector<std::string> pattern1{"ab", "cdd"};
    auto t = subject.Find(pattern1);
    EXPECT_EQ(*t[0].get(), 300);
  }
  {
    std::vector<std::string> pattern1{"ab", "cd", "ef"};
  
    auto t = subject.Find(pattern1);
    EXPECT_EQ(t.size(), 0);;
  }
  {
    std::vector<std::string> pattern1{"ab"};
  
    auto t = subject.Find(pattern1);
    EXPECT_EQ(t.size(), 0);;
  }  
}


TEST(MatcherTest, multipleMatch)
{
  MatchingEngine<int> subject;
  subject.Add({"ab", "cd"}, 100);
  subject.Add({"*", "cd"}, 200);
  subject.Add({"ab", "*"}, 300);
  {
    std::vector<std::string> pattern1{"ab", "cd"};
    auto t = subject.Find(pattern1);
    EXPECT_EQ(*t[0].get(), 100);
    EXPECT_EQ(*t[2].get(), 200);
    EXPECT_EQ(*t[1].get(), 300);
  }
  {
    std::vector<std::string> pattern1{"abb", "cd"};
    auto t = subject.Find(pattern1);
    EXPECT_EQ(*t[0].get(), 200);
  }
  {
    std::vector<std::string> pattern1{"ab", "cdd"};
    auto t = subject.Find(pattern1);
    EXPECT_EQ(*t[0].get(), 300);
  }
  {
    std::vector<std::string> pattern1{"ab", "cd", "ef"};
  
    auto t = subject.Find(pattern1);
    EXPECT_EQ(t.size(), 0);;
  }
  {
    std::vector<std::string> pattern1{"ab"};
  
    auto t = subject.Find(pattern1);
    EXPECT_EQ(t.size(), 0);;
  }
}

