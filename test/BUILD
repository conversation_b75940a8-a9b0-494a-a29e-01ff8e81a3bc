cc_library(
    name = "lib",
    srcs = glob(["lib/*.cpp"]),
    hdrs = glob(["lib/*.h"]),
    includes = ["lib"],
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "main",
    srcs = ["src/main.cpp"],
    deps = [":lib"]
)

cc_test(
    name = "hello_test",
    size = "small",
    srcs = ["test/test.cpp"],
    deps = [
        "@googletest//:gtest",
        "@googletest//:gtest_main",
        ":lib"
    ],
)