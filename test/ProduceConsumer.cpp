#include <iostream>
#include <thread>

template <typename T>
class ProducerConsumer
{
private:
    struct Node
    {
        T value;
        std::atomic<Node*> next;
        Node(const T& v): value(v), next(nullptr){};
    };
    std::atomic<Node*> head{nullptr};
    std::atomic<Node*> tail{nullptr};
    std::atomic<bool> _terminated{false};
    std::atomic<int> _size{0};

public:
    ProducerConsumer()
    {
        Node* dummyNode = new Node(T());
        head.store(dummyNode);
        tail.store(dummyNode);
    }
    ~ProducerConsumer()
    {
        Node* current = head.load();
        while (current)
        {
            Node* next = current->next.load();
            delete current;
            _size--;
            current = next;
        }
        std::cout << "~ProducerConsumer " << _size << "\n";
    }

    bool produce(const T& value)
    {
        Node* newNode = new Node(value);
        Node* oldTail = tail.load();
        Node* nullNode = nullptr;
        //single writer
        //oldTail->next.store(newNode);
        //tail.store(newNode);
        
        while (!oldTail->next.compare_exchange_strong(nullNode, newNode))
        {
            //other append new node after tail
            oldTail = tail.load();
        }
        while (!tail.compare_exchange_strong(oldTail, newNode))
        {
            // other update tail
            oldTail = tail.load();
        }
        _size++;
        return true;
    }
    bool consume(T& value)
    {
        Node* oldHead = head.load();
        while (oldHead->next.load() && !head.compare_exchange_strong(oldHead, oldHead->next.load()))
        {
            oldHead = head.load();
        }
        if (oldHead->next.load())
        {
            value = oldHead->next.load()->value;
            delete oldHead;
            _size--;
            return true;
        }
        return false;
    }
    int getSize()
    {
        return _size;
    }
    bool terminated()
    {
        return _terminated.load();
    }
    void terminate()
    {
        _terminated.store(true);

    }
};

int main()
{
    ProducerConsumer<int> pc;
    const int num_items = 10;

    std::thread producer([&pc, num_items]() {
        for (int i = 1; i <= num_items; ++i) {
            if (pc.produce(i)) {
                std::cout << "Produced: " << i << " pc _size " << pc.getSize()<< std::endl;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100)); // Simulate work
        }
        pc.terminate();
        std::cout << "Producer finished" << std::endl;
    });

    // Consumer thread
    std::thread consumer([&pc]() {
        while (true) {
            int item;
            if (pc.consume(item)) {
                std::cout << "Consumed: " << item << " pc _size " << pc.getSize() << std::endl;
            } else if (pc.terminated()) {
                
                std::cout << pc.getSize() << " terminated!\n";
                break; // Exit when terminated and queue is empty
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(150)); // Simulate work
        }
        std::cout << "Consumer finished" << std::endl;
    });
    producer.join();
    consumer.join();
    std::cout << " hello \n";
    return 0;
}