#include <iostream>
#include <map>
#include <unordered_map>
#include <list>
#include <string>

class OrderBook {
public:
    enum class Side { Buy, Sell };

    struct Order {
        uint64_t orderId;
        double price;
        uint64_t quantity;
        Side side;
    };

    struct Quote {
        double bidPrice = 0.0;
        uint64_t bidQuantity = 0;
        double askPrice = 0.0;
        uint64_t askQuantity = 0;
    };

private:
    // Price level: maps price to a list of orders at that price
    using PriceLevel = std::list<Order>;
    // Buy orders: sorted by price in descending order
    std::map<double, PriceLevel, std::greater<double>> buyOrders;
    // Sell orders: sorted by price in ascending order
    std::map<double, PriceLevel> sellOrders;
    // Order lookup by ID
    std::unordered_map<uint64_t, std::pair<double, PriceLevel::iterator>> orderLookup;
    
    template <typename PriceLevel>
    void addToPriceLevel(const Order& order, PriceLevel& priceLevels)
    {
        auto& priceLevel = priceLevels[order.price];
        priceLevel.push_back(order);
        orderLookup[order.orderId] = {order.price, std::prev(priceLevel.end())};
    }
    // Helper to add order to a price level
    void addOrderToPriceLevel(const Order& order) {
        if (order.side == Side::Buy) 
            addToPriceLevel(order,  buyOrders);
        else
            addToPriceLevel(order, sellOrders);
    }
    
    template <typename T>
    void eraseFromPriceLevel(T& priceLevels, double price,  PriceLevel::iterator it)
    {
        auto& priceLevel = priceLevels[price];
        priceLevel.erase(it);
        if (priceLevel.empty()) {
            priceLevels.erase(price);
        }
    }

    // Helper to remove order from a price level
    void removeOrderFromPriceLevel(uint64_t orderId, double price, PriceLevel::iterator it) {
        
        if (orderLookup[orderId].first != price)
            return;
        if (buyOrders.count(price))
        {
            eraseFromPriceLevel(buyOrders, price, it);
        }
        else if (sellOrders.count(price))
        {
            eraseFromPriceLevel(sellOrders, price, it);
        }

        orderLookup.erase(orderId);
    }
    bool isPassive(Order& order, double topBookPrice)
    {
        return (order.side == Side::Buy) ? order.price < topBookPrice : order.price > topBookPrice;
    }

    // Match orders and execute trades
    void matchOrder(Order& newOrder) {
        if (newOrder.side == Side::Buy) {
            matchOrder(sellOrders, newOrder);
        } else {
            matchOrder(buyOrders, newOrder);
        }
    }

    template <typename T>
    void matchOrder(T& oppositeOrders, Order& newOrder)
    { 
        while (newOrder.quantity > 0 && !oppositeOrders.empty()) {
            auto& [oppositePrice, oppositeLevel] = *oppositeOrders.begin();
            if ((newOrder.side == Side::Buy && newOrder.price < oppositePrice) ||
                (newOrder.side == Side::Sell && newOrder.price > oppositePrice)) {
                break; // No match possible
            }

            for (auto it = oppositeLevel.begin(); it != oppositeLevel.end() && newOrder.quantity > 0;) {
                auto& oppositeOrder = *it;
                uint64_t tradeQuantity = std::min(newOrder.quantity, oppositeOrder.quantity);
                newOrder.quantity -= tradeQuantity;
                oppositeOrder.quantity -= tradeQuantity;

                // Print trade execution (in a real system, this would be logged or sent to a trade engine)
                std::cout << "Trade: " << tradeQuantity << " units at price " << oppositePrice
                          << " (Order " << newOrder.orderId << " vs Order " << oppositeOrder.orderId << ")\n";

                if (oppositeOrder.quantity == 0) {
                    it = oppositeLevel.erase(it);
                    orderLookup.erase(oppositeOrder.orderId);
                } else {
                    it++;
                }
            }

            if (oppositeLevel.empty()) {
                oppositeOrders.erase(oppositePrice);
            }
        }
        if (newOrder.quantity > 0) {
            addOrderToPriceLevel(newOrder);
        }
    }

public:
    // Add a new order to the book
    void addOrder(uint64_t orderId, double price, uint64_t quantity, Side side) {
        if (quantity == 0 || price <= 0) {
            std::cerr << "Invalid order quatity or price\n";
            return;
        }
        if (orderLookup.find(orderId) != orderLookup.end()) {
            std::cerr << "Order ID " << orderId << " already exists\n";
            return;
        }

        Order order{orderId, price, quantity, side};
        matchOrder(order); // Try to match with existing orders
        if (order.quantity > 0) {
            addOrderToPriceLevel(order); // Add remaining quantity to book
        }
    }

    // Cancel an order by ID
    bool cancelOrder(uint64_t orderId) {
        auto it = orderLookup.find(orderId);
        if (it == orderLookup.end()) {
            std::cerr << "Order ID " << orderId << " not found\n";
            return false;
        }
        auto [price, levelIt] = it->second;
        removeOrderFromPriceLevel(orderId, price, levelIt);
        return true;
    }

    // Get the current quote (best bid and ask)
    Quote getQuote() const {
        Quote quote;
        if (!buyOrders.empty()) {
            const auto& [price, level] = *buyOrders.begin();
            quote.bidPrice = price;
            for (const auto& order : level) {
                quote.bidQuantity += order.quantity;
            }
        }
        if (!sellOrders.empty()) {
            const auto& [price, level] = *sellOrders.begin();
            quote.askPrice = price;
            for (const auto& order : level) {
                quote.askQuantity += order.quantity;
            }
        }
        return quote;
    }
};

// Example usage
int main() {
    OrderBook book;

    // Add some orders
    book.addOrder(1, 100.0, 50, OrderBook::Side::Buy);
    book.addOrder(2, 101.0, 30, OrderBook::Side::Buy);
    book.addOrder(3, 102.0, 20, OrderBook::Side::Sell);
    book.addOrder(4, 100.0, 40, OrderBook::Side::Sell);

    // Get quote
    auto quote = book.getQuote();
    std::cout << "Quote: Bid " << quote.bidPrice << " (" << quote.bidQuantity
              << "), Ask " << quote.askPrice << " (" << quote.askQuantity << ")\n";

    // Add a matching order
    book.addOrder(5, 102.0, 25, OrderBook::Side::Buy);

    // Get updated quote
    quote = book.getQuote();
    std::cout << "Updated Quote: Bid " << quote.bidPrice << " (" << quote.bidQuantity
              << "), Ask " << quote.askPrice << " (" << quote.askQuantity << ")\n";

    // Cancel an order
    book.cancelOrder(1);

    return 0;
}