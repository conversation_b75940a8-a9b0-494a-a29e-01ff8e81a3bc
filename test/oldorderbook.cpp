#include <map>
#include <deque>
#include <unordered_map>
#include <string>
#include <stdexcept>
#include <limits>
#include <iostream>
#include <list>

using OrderId = int;
using Price = double;
using Quantity = int;
using Timestamp = long;
using Side = enum {BID, ASK};
// Represents an order in the book
struct Order {
    OrderId order_id;
    Side side; // Side::BID or Side::ASK
    Price price;
    Quantity quantity;
    Timestamp timestamp;

    Order(OrderId id, Side s, Price p, Quantity q, Timestamp t)
        : order_id(id), side(s), price(p), quantity(q), timestamp(t) {
            std::cout << "Order id:" << id << " side:" << (s == Side::BID ? "BID" : "ASK") 
                      << " price:" << p << " quantity:" << q << " timestamp:" << t << " created\n";
        }
    
    Order() = default;
    Order(Order&& o)            = default;
    Order& operator=(Order&& mE) = default;
    Order(const Order& o)            = default;
    Order& operator=(const Order& mE) = default;
};

class OrderBook 
{
private:
    using PriceLevel = std::list<Order>;
    std::map<Price, PriceLevel, std::greater<Price>> _bids;
    std::map<Price, PriceLevel> _asks;
    // Order ID -> (Price, Iterator in Price Level)
    std::unordered_map<OrderId, std::pair<Price, PriceLevel::iterator>> _order_map;
    template <typename T>
    bool match(Order& order, T& oppositeOrders)
    {
        while (order.quantity > 0 && !oppositeOrders.empty()) {
            auto& [oppositePrice, oppositeLevel] = *oppositeOrders.begin();
            if ((order.side == Side::BID && order.price < oppositePrice) ||
                (order.side == Side::ASK && order.price > oppositePrice)) {
                break; // No match possible
            }

            for (auto it = oppositeLevel.begin(); it != oppositeLevel.end() && order.quantity > 0;) {
                auto& oppositeOrder = *it;
                Quantity tradeQuantity = std::min(order.quantity, oppositeOrder.quantity);
                order.quantity -= tradeQuantity;    
                oppositeOrder.quantity -= tradeQuantity;

                // Print trade execution (in a real system, this would be logged or sent to a trade engine)
                std::cout << "Trade: " << tradeQuantity << " units at price " << oppositePrice
                          << " (Order " << order.order_id << " vs Order " << oppositeOrder.order_id << ")\n";

                if (oppositeOrder.quantity == 0) {
                    it = oppositeLevel.erase(it);
                    _order_map.erase(oppositeOrder.order_id);
                }
            }
        };
        return order.quantity == 0;
    }
public:
    bool add_order(OrderId order_id, Side side, Price price, Quantity quantity, Timestamp timestamp) 
    {
        if (quantity <= 0 || price <= 0) {
            // std::invalid_argument("Invalid quantity or price");
            return false; 
        }
        if (_order_map.find(order_id) != _order_map.end()) {
            // std::invalid_argument("Order ID already exists");
            return false; 
        }
        Order order(order_id, side, price, quantity, timestamp);
        if (side == Side::BID)
        {
            if (!match(order, _asks))
            {
                _bids[price].push_back(order);
                _order_map[order_id] = {price, std::prev(_bids[price].end())};
            }
            
        }
        else
        {
            if (!match(order, _bids))
            {
                _asks[price].push_back(order);
                _order_map[order_id] = {price, std::prev(_asks[price].end())};
            }
        }

        return true;
    }
    bool remove_order(OrderId order_id) 
    {
        auto it = _order_map.find(order_id);
        if (it == _order_map.end()) {
            return false;
        }
        auto [price, order_it] = it->second;
        if (order_it->side == Side::BID)
        {
            _bids[price].erase(order_it);
            if (_bids[price].empty())
            {
                _bids.erase(price);
            }
        }
        else
        {
            _asks[price].erase(order_it);
            if (_asks[price].empty())
            {
                _asks.erase(price);
            }
        }   
        return true;
    }

    template <typename T>
    std::pair<Price, Quantity> getTop(const T& pricelevel) const
    {
        if (pricelevel.empty())
        {
            return {0.0, 0};
        }
        else
        {
            auto& [price, level] = *pricelevel.begin();
            Quantity quantity = 0;
            for (const auto& order : level) {
                quantity += order.quantity;
            }
            return {price, quantity};
        }
    }
    struct MarketQuote {
        Price best_bid;
        Quantity bid_quantity;
        Price best_ask;
        Quantity ask_quantity;
    };
    MarketQuote get_market_quote() const 
    {
        MarketQuote quote;
        auto [bid_price, bid_quantity] = getTop(_bids);
        quote.best_bid = bid_price;
        quote.bid_quantity = bid_quantity;
        auto [ask_price, ask_quantity] = getTop(_asks);
        quote.best_ask = ask_price;
        quote.ask_quantity = ask_quantity;

        return quote;
    }
};


// class OrderBook {
// private:
//     // Bids: price -> deque of orders (highest price first)
//     std::map<double, std::deque<Order>, std::greater<double>> bids;
//     // Asks: price -> deque of orders (lowest price first)
//     std::map<double, std::deque<Order>> asks;
//     // Order ID -> Order for fast lookup
//     std::unordered_map<int, Order> order_map;
//     const int MAX_QUANTITY = 1000;

// public:
//     // Add a new order to the book
//     std::string add_order(int order_id, const std::string& side, double price, int quantity, long timestamp) {
//         // Validate inputs
//         if (price <= 0) {
//             return "Error: Price must be positive";
//         }
//         if (quantity <= 0 || quantity > MAX_QUANTITY) {
//             return "Error: Quantity must be positive and <= " + std::to_string(MAX_QUANTITY);
//         }
//         if (side != Side::BID && side != Side::ASK) {
//             return "Error: Invalid side";
//         }
//         if (order_map.find(order_id) != order_map.end()) {
//             return "Error: Order ID already exists";
//         }

//         // Create and store order
//         Order order(order_id, side, price, quantity, timestamp);
//         order_map[order_id] = order;

//         // Add to appropriate book
//         if (side == Side::BID)
//         {
//             bids[price].push_back(order);

//         }
//         else
//         {
//             asks[price].push_back(order);
//         }
//         return "Order added successfully";
//     }

//     // Remove an order by ID
//     std::string remove_order(int order_id) {
//         auto it = order_map.find(order_id);
//         if (it == order_map.end()) {
//             return "Error: Order not found";
//         }

//         const Order& order = it->second;
//         if (order.side == Side::BID)
//         {
//             auto& orders = bids[order.price];

//             // Remove order from deque
//             for (auto order_it = orders.begin(); order_it != orders.end(); ++order_it) {
//                 if (order_it->order_id == order_id) {
//                     orders.erase(order_it);
//                     break;
//                 }
//             }

//             // Clean up empty price level
//             if (orders.empty()) {
//                 bids.erase(order.price);
//             }
//         }
//         else
//         {
//             auto& orders = asks[order.price];

//             // Remove order from deque
//             for (auto order_it = orders.begin(); order_it != orders.end(); ++order_it) {
//                 if (order_it->order_id == order_id) {
//                     orders.erase(order_it);
//                     break;
//                 }
//             }

//             // Clean up empty price level
//             if (orders.empty()) {
//                 asks.erase(order.price);
//             }

//         }
        

//         // Remove from order map
//         order_map.erase(order_id);
//         return "Order removed successfully";
//     }

//     // Get market quote (best bid/ask and aggregated quantities)
//     struct MarketQuote {
//         double best_bid;
//         int bid_quantity;
//         double best_ask;
//         int ask_quantity;

//         MarketQuote() 
//             : best_bid(0), bid_quantity(0), 
//               best_ask(std::numeric_limits<double>::max()), ask_quantity(0) {}
//     };

//     MarketQuote get_market_quote() const {
//         MarketQuote quote;

//         // Best bid (highest price)
//         if (!bids.empty()) {
//             auto& orders = bids.begin()->second;
//             quote.best_bid = bids.begin()->first;
//             for (const auto& order : orders) {
//                 quote.bid_quantity += order.quantity;
//             }
//         }

//         // Best ask (lowest price)
//         if (!asks.empty()) {
//             auto& orders = asks.begin()->second;
//             quote.best_ask = asks.begin()->first;
//             for (const auto& order : orders) {
//                 quote.ask_quantity += order.quantity;
//             }
//         }

//         return quote;
//     }
// };

// Example usage
int main() {
    OrderBook ob;

    // Add sample orders
    std::cout << ob.add_order(1, Side::BID, 100.50, 100, 1) << std::endl;
    std::cout << ob.add_order(2, Side::BID, 100.00, 200, 2) << std::endl;
    std::cout << ob.add_order(3, Side::ASK, 101.00, 150, 3) << std::endl;
    std::cout << ob.add_order(4, Side::ASK, 101.50, 100, 4) << std::endl;

    // Invalid orders
    std::cout << ob.add_order(5, Side::BID, -100, 100, 5) << std::endl;
    std::cout << ob.add_order(6, Side::ASK, 102, 2000, 6) << std::endl;

    // Get market quote
    auto quote = ob.get_market_quote();
    std::cout << "\nMarket Quote:\n";
    std::cout << "Best Bid: " << (quote.best_bid > 0 ? std::to_string(quote.best_bid) : "None")
              << " (Qty: " << quote.bid_quantity << ")\n";
    std::cout << "Best Ask: " << (quote.best_ask < std::numeric_limits<double>::max() ? std::to_string(quote.best_ask) : "None")
              << " (Qty: " << quote.ask_quantity << ")\n";

    // Remove an order
    std::cout << ob.remove_order(1) << std::endl;
    std::cout << "\nAfter removing order 1:\n";
    quote = ob.get_market_quote();
    std::cout << "Best Bid: " << (quote.best_bid > 0 ? std::to_string(quote.best_bid) : "None")
              << " (Qty: " << quote.bid_quantity << ")\n";
    std::cout << "Best Ask: " << (quote.best_ask < std::numeric_limits<double>::max() ? std::to_string(quote.best_ask) : "None")
              << " (Qty: " << quote.ask_quantity << ")\n";

    return 0;
}
