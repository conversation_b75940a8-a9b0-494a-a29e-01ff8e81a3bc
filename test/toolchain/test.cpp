#include <atomic>

template <tyename T>
class LookFreeQueue
{
private:
    struct Node
    {
        T _value;
        std::atomic<Node *> _next;
        Node (const T& v) : _value(v), _next(nullptr) {};
    };
    std::atomic<Node *> _head;
    std::atomic<Node *> _tail;
    

public:
    LockFreeQueue()
    {
        Node* dummy = new Node(T{});
        _head.store(dummy);
        _tail.store(dummy);
    }
    ~LockFreeQueue()
    {
        Node* current = _head._next.load();
        while (current)
        {
            
        }

    }

};