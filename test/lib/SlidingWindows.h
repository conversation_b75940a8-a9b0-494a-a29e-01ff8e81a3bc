#pragma once
#include <iostream>
#include <vector>
#include <unordered_map>

struct Entry {
    int quantity;
    double price;
    
    Entry(int q, double p)
        : quantity(q), price(p) {}
};

class Aggregator {
public:
    void addEntry(const Entry& entry, long long timeKey) {
        currentAggregates[timeKey].quantity += entry.quantity;
        currentAggregates[timeKey].notionalValue += entry.quantity * entry.price;
    }

    void printAggregates() const {
        std::cout << "Aggregated Results per Second:\n";
        for (const auto& [timeKey, aggregate] : currentAggregates) {
            std::cout << "Time (s): " << timeKey
                      << ", Total Quantity: " << aggregate.quantity
                      << ", Total Notional Value: " << aggregate.notionalValue << std::endl;
        }
    }

private:
    struct Aggregate {
        int quantity = 0;
        double notionalValue = 0.0;
    };

    std::unordered_map<long long, Aggregate> currentAggregates; // Key: time in seconds
};

