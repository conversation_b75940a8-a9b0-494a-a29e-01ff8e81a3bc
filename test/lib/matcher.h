#pragma once

#include <iostream>
#include <unordered_map>
#include <vector>
#include <string>
#include <memory>
#include <optional>

template <typename T>
class TrieNode
{
private:
    std::unordered_map<std::string, std::unique_ptr<TrieNode<T>>> _childrens;
    std::shared_ptr<T> _value;
    bool _isLeave = false;

public:
    TrieNode() {}

    ~TrieNode() {}

    TrieNode *Get(std::string key)
    {
        auto iter = _childrens.find(key);
        if (iter == _childrens.end())
        {
            auto res = _childrens.insert(std::make_pair(key, std::make_unique<TrieNode<int>>()));
            iter = res.first;
        }
        return iter->second.get();
    }

    TrieNode *Find(std::string key)
    {
        auto iter = _childrens.find(key);
        if (iter != _childrens.end())
        {
            return iter->second.get();
        }
        return nullptr;
    }

    void SetValue(T x)
    {
        _value = std::make_shared<T>(x);
        _isLeave = true;
    }

    void UnsetValue()
    {
        _value.reset();
        _isLeave = false;
    }
    std::shared_ptr<T> GetValue()
    {
        std::cout << "GetValue " << _isLeave << "\n";
        if (_isLeave)
            return _value;
        return nullptr;
    }
};

template <typename T>
class MatchingEngine
{
private:
    std::unique_ptr<TrieNode<T>> _root;

public:
    using Pattern = std::vector<std::string>;
    MatchingEngine()
        : _root{new TrieNode<int>()}
    {
        std::cout << "MathingEngine Ctor\n";
    }

    ~MatchingEngine()
    {
        std::cout << "MathingEngine Dtor\n";
    }
    void Add(const Pattern &input, int value)
    {
        auto *temp = _root.get();
        for (auto i : input)
        {
            std::cout << "adding " << i << "\n";
            temp = temp->Get(i);
        }
        temp->SetValue(value);
    }

    std::vector<std::shared_ptr<T>> Find(const Pattern &keys)
    {
        return Find_Impl(_root.get(), keys.begin(), keys.end());
    }

    std::vector<std::shared_ptr<T>> Find_Impl(TrieNode<T> *temp,
                                              Pattern::const_iterator start,
                                              Pattern::const_iterator end)
    {
        if (!temp)
            return {};
        std::vector<std::shared_ptr<T>> result;
        if (start != end)
        {
            {
                auto r1 = Find_Impl(temp->Find(*start), std::next(start), end);
                result.insert(result.end(), std::make_move_iterator(r1.begin()), std::make_move_iterator(r1.end()));
            }
            {
                auto r1 = Find_Impl(temp->Find("*"), std::next(start), end);
                result.insert(result.end(), std::make_move_iterator(r1.begin()), std::make_move_iterator(r1.end()));
            }
        }
        else
        {
            auto t = temp->GetValue();
            if (t)
                result.emplace_back(t);
        }
        return result;
    }
};

template <typename T>
class Test
{
public:
    Test(T t) : _t(t) {};
    template <typename U>
    void Print(U u)
    {
        std::cout << "Default\n";
        std::cout << _t << " " << u << "\n";
    }

    template <>
    void Print(double u)
    {
        std::cout << "Double\n";
        std::cout << _t << " " << u << "\n";
    }
private:
    T _t;
};

template<> template<>
void Test<double>::Print(double u)
{
    std::cout << "Spec double\n";
    std::cout << _t << " " << u << "\n";
}