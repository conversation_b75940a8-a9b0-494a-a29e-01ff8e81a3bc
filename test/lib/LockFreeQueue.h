#include <atomic>
#include <memory>

template<typename T>
class LockFreeQueue {
private:
    struct Node {
        T data;
        std::atomic<Node*> next;
        Node(const T& value) : data(value), next(nullptr) {}
    };

    std::atomic<Node*> head;
    std::atomic<Node*> tail;

public:
    LockFreeQueue() {
        Node* dummy = new Node(T());
        head.store(dummy);
        tail.store(dummy);
    }

    ~LockFreeQueue() {
        Node* current = head.load();
        while (current) {
            Node* next = current->next.load();
            delete current;
            current = next;
        }
    }

    void enqueue(const T& value) {
        Node* newNode = new Node(value);
        Node* oldTail;
        Node* nullNode = nullptr;

        while (true) {
            oldTail = tail.load();
            if (oldTail->next.compare_exchange_strong(nullNode, newNode)) {
                tail.compare_exchange_strong(oldTail, newNode);
                break;
            } else {
                tail.compare_exchange_strong(oldTail, oldTail->next.load());
            }
        }
    }

    bool dequeue(T& result) {
        Node* oldHead;
        Node* newHead;
        Node* next;

        while (true) {
            oldHead = head.load();
            next = oldHead->next.load();
            
            if (!next) {
                return false; // Queue is empty
            }

            newHead = next;
            if (head.compare_exchange_strong(oldHead, newHead)) {
                result = newHead->data;
                delete oldHead;
                return true;
            }
        }
    }
};