load("@aspect_rules_js//npm:defs.bzl", "npm_package")
load("@aspect_rules_ts//ts:defs.bzl", "ts_project")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages(name = "node_modules")

ts_project(
    name = "one_ts",
    srcs = ["src/main.ts"],
    # runtime direct dependencies of the linked npm package
    data = [
        ":node_modules/is-odd",
    ],
    declaration = True,
    transpiler = "tsc",
    # transpile time direct dependencies
    deps = [
        ":node_modules/@types/is-odd",
        "//next.js:node_modules/@types/node",
    ],
)

# make this library available via node_modules
npm_package(
    name = "pkg",
    srcs = [
        "package.json",
        ":one_ts",
    ],
    visibility = ["//visibility:public"],
)
