load("@aspect_rules_js//js:defs.bzl", "js_library", "js_test")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//next.js:next/package_json.bzl", next_bin = "bin")
load("//next.js:defs.bzl", "next")

npm_link_all_packages(name = "node_modules")

next_bin.next_binary(
    name = "next_js_binary",
    visibility = ["//visibility:public"],
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//visibility:public"],
)

next(
    name = "next",
    srcs = [
        "//next.js/pages",
        "//next.js/public",
        "//next.js/styles",
    ],
    data = [
        "next.config.js",
        "package.json",
        ":node_modules/@bazel-example/one",
        ":node_modules/is-even",
        ":node_modules/next",
        ":node_modules/react",
        ":node_modules/react-dom",
        ":node_modules/typescript",
    ],
    next_bin = "./node_modules/next/dist/bin/next",
    next_js_binary = ":next_js_binary",
)

# A smoke test on the artifact produced by the :next target
js_test(
    name = "build_smoke_test",
    chdir = package_name(),
    data = [":next"],
    entry_point = "build_smoke_test.js",
)

js_library(
    name = "package_json",
    srcs = ["package.json"],
    visibility = ["//visibility:public"],
)

js_library(
    name = "jest_config",
    srcs = ["jest.config.js"],
    visibility = ["//visibility:public"],
    deps = [":node_modules/next"],
)

js_library(
    name = "eslintrc",
    srcs = [".eslintrc.js"],
    visibility = ["//:__subpackages__"],
    deps = [
        ":node_modules/@next/eslint-plugin-next",
        ":node_modules/eslint-config-next",
    ],
)

build_test(
    name = "build_test",
    targets = [
        ":next",
        # Not build testing the `:next_export` target since this `next export` writes back to the `.next` directory which
        # causes issues with trying to write to an input. See https://github.com/vercel/next.js/issues/43344.
        # TODO: fix in Next.js (https://github.com/vercel/next.js/issues/43344) or find work-around.
        # ":next_export",
    ],
)
