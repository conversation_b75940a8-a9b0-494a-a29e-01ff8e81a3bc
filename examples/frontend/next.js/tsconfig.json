{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "declaration": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}