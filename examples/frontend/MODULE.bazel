"Bazel module dependencies, see https://bazel.build/external/overview#bzlmod"

bazel_dep(name = "aspect_bazel_lib", version = "2.7.7")
bazel_dep(name = "aspect_rules_lint", version = "1.0.0-rc4")
bazel_dep(name = "aspect_rules_jest", version = "0.22.0-rc0")
bazel_dep(name = "aspect_rules_js", version = "2.0.0-rc1")
bazel_dep(name = "aspect_rules_swc", version = "2.0.0-rc0")
bazel_dep(name = "aspect_rules_ts", version = "3.0.0-rc0")
bazel_dep(name = "aspect_rules_rollup", version = "2.0.0-rc0")
bazel_dep(name = "aspect_rules_webpack", version = "0.16.0-rc0")
bazel_dep(name = "bazel_skylib", version = "1.7.1")
bazel_dep(name = "rules_nodejs", version = "6.1.1")

node = use_extension("@rules_nodejs//nodejs:extensions.bzl", "node", dev_dependency = True)
node.toolchain(node_version = "20.13.1")

pnpm = use_extension("@aspect_rules_js//npm:extensions.bzl", "pnpm", dev_dependency = True)
use_repo(pnpm, "pnpm")

npm = use_extension("@aspect_rules_js//npm:extensions.bzl", "npm", dev_dependency = True)
npm.npm_translate_lock(
    name = "npm",
    npmrc = "//:.npmrc",
    pnpm_lock = "//:pnpm-lock.yaml",
    public_hoist_packages = {
        "@next/eslint-plugin-next": ["next.js"],
        "eslint-config-react-app": ["react"],
        "eslint@8.28.0": ["react"],
    },
    verify_node_modules_ignored = "//:.bazelignore",
)
use_repo(npm, "npm")

rules_ts_ext = use_extension(
    "@aspect_rules_ts//ts:extensions.bzl",
    "ext",
    dev_dependency = True,
)
rules_ts_ext.deps()
use_repo(rules_ts_ext, "npm_typescript")
