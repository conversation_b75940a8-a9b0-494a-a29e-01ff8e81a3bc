
## Available Scripts

### `npm start`

Runs the app in the development mode using <PERSON><PERSON> as the devserver.

Uses SWC for transpiling typescript.

Thanks to `js_run_devserver`, the page will reload as you make source code edits.

### `npm test`

Launches the test runner in the interactive watch mode.

It runs several test targets, including:

- typechecking with `tsc`
- linting with `eslint`
- unit tests with `vitest`

As you save source code changes, the affected tests will be automatically re-run.

### `npm run build`

Builds the app for production to the `dist` folder.
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.<br />
Your app is ready to be deployed!

### `npm run serve`

Serves the production build assets to your browser.
