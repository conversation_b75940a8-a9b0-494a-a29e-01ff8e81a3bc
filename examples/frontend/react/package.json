{"name": "create-react-app", "version": "0.1.0", "private": true, "dependencies": {"react": "~18.2.0", "react-dom": "~18.2.0", "web-vitals": "2.1.4"}, "devDependencies": {"@bazel/ibazel": "0.25.0", "@testing-library/jest-dom": "4.2.4", "@testing-library/react": "13.4.0", "@testing-library/user-event": "14.4.3", "@types/jest": "29.2.3", "@types/react": "~18.2.0", "@typescript-eslint/eslint-plugin": "5.44.0", "@typescript-eslint/parser": "5.44.0", "eslint-config-react-app": "^7.0.1", "@vitejs/plugin-react": "^4.2.0", "vite-plugin-svgr": "^4.2.0", "jest-environment-jsdom": "29.3.1", "jest-junit": "16.0.0", "jest-transform-stub": "2.0.0", "tsconfig-to-swcconfig": "2.4.0", "typescript": "4.9.3", "vite": "3.2.10"}, "scripts": {"start": "<PERSON><PERSON><PERSON> run start", "build": "bazel build build", "serve": "bazel run preview", "test": "ibazel test src/..."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}