{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "noImplicitAny": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ES2020", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "jsx": "react-jsx"}, "include": ["src"]}