"Root BUILD file for all frontend examples"

load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//:eslint/package_json.bzl", eslint_bin = "bin")

package(default_visibility = ["//:__subpackages__"])

# Create the root of the "virtual store" of npm dependencies under bazel-out.
# This must be done in the package where the pnpm workspace is rooted.
npm_link_all_packages(name = "node_modules")

eslint_bin.eslint_binary(name = "eslint")
