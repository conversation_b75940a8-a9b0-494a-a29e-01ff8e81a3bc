{"name": "@bazel-example/vue", "version": "0.0.0", "scripts": {"dev": "i<PERSON><PERSON> run :vite", "build": "run-p type-check build-only", "preview": "vite preview --port 4173", "build-only": "bazel build :build", "type-check": "bazel test :type-check"}, "dependencies": {"@bazel-example/vue-library": "workspace:*", "vue": "3.2.45", "vue-router": "4.1.6"}, "devDependencies": {"@bazel/ibazel": "0.25.0", "@types/node": "18.19.31", "@vitejs/plugin-vue": "3.2.0", "@vitejs/plugin-vue-jsx": "2.1.1", "@vue/tsconfig": "0.1.3", "npm-run-all": "4.1.5", "typescript": "4.9.3", "vite": "3.2.10", "vue-tsc": "1.0.9"}}