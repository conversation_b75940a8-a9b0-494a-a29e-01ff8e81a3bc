"""Export all files for vite.

Since Vue doesn't natively support <PERSON><PERSON>, we just wrap the build system they do support.
This does not provide <PERSON><PERSON>'s incrementality benefits.
"""

load("@aspect_bazel_lib//lib:copy_to_bin.bzl", "copy_to_bin")

copy_to_bin(
    name = "src",
    srcs = glob([
        "**/*.css",
        "**/*.svg",
        "**/*.ts",
        "**/*.vue",
    ]),
    visibility = ["//vue:__pkg__"],
)
