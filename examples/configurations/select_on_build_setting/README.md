This is an example of how to read build settings in a [configurable attribute](https://bazel.build/docs/configurable-attributes) aka a `select()`. 

To test it out, cd to this directory and run the following
```
$ bazel build :harvest # => "We're harvesting apples!"
$ bazel build :harvest --//select_on_build_setting:season=summer # => "We're harvesting cherries!"
$ bazel build :harvest --//select_on_build_setting:season=fall # => "We're harvesting pumpkins!"
```
