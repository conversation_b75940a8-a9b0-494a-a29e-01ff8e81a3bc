---
tasks:
  java-maven-linux:
    name: "Maven Java App"
    platform: ubuntu2004
    working_directory: ../java-maven
    build_targets:
    - "..."
    test_targets:
    - "..."
  java-maven-macos:
    name: "Maven Java App"
    platform: macos
    working_directory: ../java-maven
    build_targets:
    - "..."
    test_targets:
    - "..."
    test_flags:
    # FIXME: either have a docker/podman runtime on our Mac images,
    # or use a remote container service like TestContainers Cloud
    - "--test_tag_filters=-requires-docker"
  java-maven-windows:
    name: "Maven Java App"
    platform: windows
    working_directory: ../java-maven
    test_targets:
    # FIXME: Our Windows CI machines need some DLL and/or Visual C++ redistributable.
    # running bsdtar.exe results in `Exit -1073741515: STATUS_DLL_NOT_FOUND`
    # for now, only run the java_test target.
    - "//:tests"
  third-party-dependencies-linux:
    name: "Example with third party dependencies"
    platform: ubuntu1804
    working_directory: ../third-party-dependencies
    build_targets:
    - "..."
  third-party-dependencies-macos:
    name: "Example with third party dependencies"
    platform: macos
    working_directory: ../third-party-dependencies
    build_targets:
    - "..."
  third-party-dependencies-windows:
    name: "Example with third party dependencies"
    platform: windows
    working_directory: ../third-party-dependencies
    build_targets:
    - "..."
