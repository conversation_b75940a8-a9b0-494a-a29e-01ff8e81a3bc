---
matrix:
  all_platform: ["ubuntu1804", "macos", "windows"]

tasks:
  01-depend_on_bazel_module:
    name: "Depend on Bazel module"
    platform: ${{ all_platform }}
    working_directory: ../bzlmod/01-depend_on_bazel_module
    build_targets:
    - "//:main"
  02-override_bazel_module:
    name: "Override Bazel module"
    platform: ${{ all_platform }}
    working_directory: ../bzlmod/02-override_bazel_module
    build_targets:
    - "//:main"
  03-introduce_dependencies_with_module_extension:
    name: "Introduce dependencies with module extension"
    platform: ${{ all_platform }}
    working_directory: ../bzlmod/03-introduce_dependencies_with_module_extension
    build_targets:
    - "//:city_count"
    - "//:emoji_count"
  04-local_config_and_register_toolchains:
    name: "Local config and register toolchains"
    platform: ${{ all_platform }}
    environment:
      MY_SHELL_BIN_PATH: /foo/bar/sh
    working_directory: ../bzlmod/04-local_config_and_register_toolchains
    build_targets:
    - "//:get_sh_path"
  05-integrate_third_party_package_manager:
    name: "Integrate third party package manager"
    platform: ${{ all_platform }}
    working_directory: ../bzlmod/05-integrate_third_party_package_manager
    build_targets:
    - "//:check_books"
  06-specify_dev_dependency:
    name: "Specify dev dependency"
    platform: ${{ all_platform }}
    working_directory: ../bzlmod/06-specify_dev_dependency
    build_targets:
    - "//:check_books"
