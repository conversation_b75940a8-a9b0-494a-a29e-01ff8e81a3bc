load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")
load(":file_collector.bzl", "file_collector")

cc_library(
    name = "lib",
    srcs = [
        "lib.cc",
    ],
    hdrs = [
        "lib.h",
    ],
)

cc_binary(
    name = "app",
    srcs = [
        "app.cc",
    ],
    deps = [":lib"],
)

file_collector(
    name = "h_file_count",
    extension = "h",
    deps = ["app"],
)

file_collector(
    name = "file_count",
    deps = [
        "app",
        "lib",
    ],
)
