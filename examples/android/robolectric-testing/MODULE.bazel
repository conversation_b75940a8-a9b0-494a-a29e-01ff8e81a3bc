"Bazel dependencies"

bazel_dep(name = "platforms", version = "0.0.10")
bazel_dep(name = "rules_jvm_external", version = "5.3")

maven = use_extension("@rules_jvm_external//:extensions.bzl", "maven")
maven.install(
     artifacts = [
        "org.robolectric:robolectric:4.9",
        "junit:junit:4.13.2",
        "com.google.truth:truth:1.1.3",
        "org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10",
        "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10",
        "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.10",
        "org.jetbrains.kotlin:kotlin-stdlib:1.7.10",
    ],
    repositories = [
        "https://maven.google.com",
        "https://repo1.maven.org/maven2",
    ],
)

use_repo(maven, "maven")
