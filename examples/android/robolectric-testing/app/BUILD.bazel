load("@io_bazel_rules_kotlin//kotlin:android.bzl", "kt_android_library", "kt_android_local_test")

kt_android_library(
    name = "lib",
    srcs = glob(["src/main/java/**/*.kt"]),
    custom_package = "com.example.android.bazel",
    manifest = "src/main/AndroidManifest.xml",
    resource_files = glob(["src/main/res/**"]),
    deps = [
        "@maven//:org_jetbrains_kotlin_kotlin_stdlib",
        "@maven//:org_jetbrains_kotlin_kotlin_stdlib_common",
        "@maven//:org_jetbrains_kotlin_kotlin_stdlib_jdk7",
        "@maven//:org_jetbrains_kotlin_kotlin_stdlib_jdk8",
    ],
)

kt_android_local_test(
    name = "test",
    srcs = ["src/test/java/com/example/android/bazel/WelcomeActivityTest.kt"],
    custom_package = "com.example.android.bazel.test",
    test_class = "com.example.android.bazel.WelcomeActivityTest",
    deps = [
        ":lib",
        "@maven//:com_google_truth_truth",
        "@maven//:junit_junit",
        "@maven//:org_robolectric_robolectric",
        "@robolectric//bazel:android-all",
    ],
)
