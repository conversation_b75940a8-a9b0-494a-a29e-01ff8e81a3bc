"Bazel dependencies"

bazel_dep(name = "platforms", version = "0.0.10")
bazel_dep(name = "rules_jvm_external", version = "5.3")

maven = use_extension("@rules_jvm_external//:extensions.bzl", "maven")
maven.install(
    artifacts = [
        "androidx.appcompat:appcompat:1.5.1",
        # Jetpack Compose Dependencies
        "androidx.activity:activity-compose:1.6.0",
        "androidx.compose.material:material:1.2.1",
        "androidx.compose.ui:ui:1.2.1",
        "androidx.compose.ui:ui-tooling:1.2.1",
        "androidx.compose.compiler:compiler:1.3.2",
        "androidx.compose.runtime:runtime:1.2.1",
        # Dependencies needed to manage version conflicts
        "androidx.core:core:1.6.0",
        "androidx.core:core-ktx:1.6.0",
        "androidx.savedstate:savedstate-ktx:1.2.0",
        "androidx.savedstate:savedstate:1.2.0",
        "androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1",
        "androidx.lifecycle:lifecycle-livedata-core:2.5.1",
        "androidx.lifecycle:lifecycle-livedata:2.5.1",
        "androidx.lifecycle:lifecycle-process:2.5.1",
        "androidx.lifecycle:lifecycle-runtime-ktx:2.5.1",
        "androidx.lifecycle:lifecycle-runtime:2.5.1",
        "androidx.lifecycle:lifecycle-service:2.5.1",
        "androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1",
        "androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1",
        "androidx.lifecycle:lifecycle-viewmodel:2.5.1",
    ],
    repositories = [
        "https://maven.google.com",
        "https://repo1.maven.org/maven2",
    ],
)
use_repo(maven, "maven")
