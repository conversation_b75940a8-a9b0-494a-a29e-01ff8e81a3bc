load("@rules_android//android:rules.bzl", "android_binary", "android_library")
load("@rules_cc//cc:defs.bzl", "cc_library")

android_library(
    name = "lib",
    srcs = ["java/com/example/android/bazel/MainActivity.java"],
    custom_package = "com.example.android.bazel",
    manifest = "LibraryManifest.xml",
    resource_files = glob(["res/**/*"]),
    deps = [
        ":jni_lib",
        "@maven//:androidx_appcompat_appcompat",
        "@maven//:androidx_constraintlayout_constraintlayout",
    ],
)

cc_library(
    name = "jni_lib",
    srcs = ["cpp/native-lib.cpp"],
)

android_binary(
    name = "app",
    manifest = "AndroidManifest.xml",
    manifest_values = {"applicationId": "com.example.android.bazel"},
    deps = [":lib"],
)
