load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")

RULES_ANDROID_COMMIT = "93e27030d3f0defa39cbbc35195638cb772b0c27"

http_archive(
    name = "rules_android",
    sha256 = "71cae2413868a24f17d43fd595af6f3905d2e5b3235f76514f54800bfd90c903",
    strip_prefix = "rules_android-" + RULES_ANDROID_COMMIT,
    urls = ["https://github.com/bazelbuild/rules_android/archive/%s.zip" % RULES_ANDROID_COMMIT],
)

load("@rules_android//:prereqs.bzl", "rules_android_prereqs")
rules_android_prereqs()
load("@rules_android//:defs.bzl", "rules_android_workspace")
rules_android_workspace()

load("@rules_android//rules:rules.bzl", "android_sdk_repository")
# Requires that the ANDROID_HOME environment variable is set to the Android SDK path.
android_sdk_repository(
    name = "androidsdk",
)

register_toolchains(
    "@rules_android//toolchains/android:android_default_toolchain",
    "@rules_android//toolchains/android_sdk:android_sdk_tools",
)

http_archive(
    name = "rules_android_ndk",
    sha256 = "b1a5ddd784e6ed915c2035c0db536a278b5f50c64412128c06877115991391ef",
    strip_prefix = "rules_android_ndk-877c68ef34c9f3353028bf490d269230c1990483",
    url = "https://github.com/bazelbuild/rules_android_ndk/archive/877c68ef34c9f3353028bf490d269230c1990483.zip",
)

load("@rules_android_ndk//:rules.bzl", "android_ndk_repository")

android_ndk_repository(name = "androidndk")

register_toolchains("@androidndk//:all")
