# Contributing Financially

This repository welcomes community funding of new examples, via the
[Rules Authors SIG](https://github.com/bazelbuild/community/blob/main/sigs/rules-authors/CHARTER.md).

You may contribute at <https://opencollective.com/bazel-rules-authors-sig>.

If you'd like your donation to be used to fund development of a specific example of Bazel usage,
please file an issue on this repository first, describing your scenario, and indicate that you
intend to provide a "feature bounty" for it in the form of a donation.

# Contributing Code

We put these examples together because we needed them, and because
they seemed instructive. We encourage you to come up with variations
of this within your own source tree.

Should wish to contribute examples back, please sign Google's CLA
before sending us patches: https://cla.developers.google.com/

The CLA is necessary mainly because you own the copyright to your
changes, even after your contribution becomes part of our codebase, so
we need your permission to use and distribute your code. We also need
to be sure of various other things - for instance that you'll tell us
if you know that your code infringes on other people's patents. You
don't have to sign the CLA until after you've submitted your code for
review and a member has approved it, but you must do it before we can
put your code into our codebase.

Before you start working on a larger contribution, you should get in
touch with us <NAME_EMAIL>.

### The small print
Contributions made by corporations are covered by a different agreement than
the one above, the Software Grant and Corporate Contributor License Agreement.
