load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository")
load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")

# Use a fork version of glog with http_archive.
http_archive(
    name = "com_github_google_glog",
    sha256 = "e544cb12dc528b8951452e3968f4034fc28aaa81f2859cba05b5832dbe5dfa8f",
    strip_prefix = "glog-0faaf10108e9894276b0de593d0f98330db9ea88",
    urls = ["https://github.com/meteorcloudy/glog/archive/0faaf10108e9894276b0de593d0f98330db9ea88.tar.gz"],
)

# Use a fork version of gflags with git_repository.
git_repository(
    name = "com_github_gflags_gflags",
    commit = "95995169e86f3fedd19696df5b1811d441c462a2",
    remote = "https://github.com/meteorcloudy/gflags.git",
)

# Use a patched version of bazel_skylib
http_archive(
    name = "bazel_skylib",
    patch_args = ["-p1"],
    patches = ["//:bazel_skylib.patch"],
    sha256 = "bc283cdfcd526a52c3201279cda4bc298652efa898b10b4db0837dc51652756f",
    urls = ["https://github.com/bazelbuild/bazel-skylib/releases/download/1.7.1/bazel-skylib-1.7.1.tar.gz"],
)

# Depend on lib_a at a local path.
local_repository(
    name = "lib_a",
    path = "./lib_a",
)
