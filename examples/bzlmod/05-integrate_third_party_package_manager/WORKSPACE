# lib_a requires the_great_gatsby@2003.7, hamlet@1800.1
# lib_b requires the_great_gatsby@2020.5, hamlet@1603.1
# The main repo requires the_great_gatsby@1995.12, hamlet@2005.1
# Question: Guess which book edition you'll get with the following definitions? :)

local_repository(
    name = "librarian",
    path = "../utils/librarian",
)

local_repository(
    name = "lib_a",
    path = "./lib_a",
)

load("@lib_a//:deps.bzl", "fetch_book_for_lib_a")
load("@librarian//:librarian.bzl", "fetch_book")

fetch_book(
    name = "the_great_gatsby",
    edition = "1995.12",
)

fetch_book_for_lib_a()

fetch_book(
    name = "hamlet",
    edition = "2005.1",
)

local_repository(
    name = "lib_b",
    path = "./lib_b",
)

load("@lib_b//:deps.bzl", "fetch_book_for_lib_b")

fetch_book_for_lib_b()

# Answer: You'll get the_great_gatsby@2003.7 and hamlet@2005.1.
# To verify run: bazel build //:check_books && cat bazel-bin/books
# Here is how to determine which repo definition wins in the WORKSPACE file:
#   - Split the WORKSPACE file into chunks by "load" statements.
#   - If a repo is defined in multiple chunks, the first chunk wins.
#   - If a repo is defined multiple times in the same chunk, the last definition wins.
# Moving around the load statements will result different repository definitions,
# this is a perfect example why handling external dependencies with WORKSPACE is so difficult.
