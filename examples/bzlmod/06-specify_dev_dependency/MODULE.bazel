module(
    name = "example",
    version = "0.0.1",
)

# lib_a requires
#   - bazel_skylib@1.2.0 as dev dependency.
#   - the_great_gatsby@2003.7 as dependency.
#   - hamlet@2005.1 as dev dependency.
bazel_dep(name = "lib_a", version = "")
local_path_override(
    module_name = "lib_a",
    path = "./lib_a",
)

# The root module will get bazel_skylib 1.1.1 because bazel_skylib 1.2.0 is only a dev dependency of lib_a.
bazel_dep(
    name = "bazel_skylib",
    version = "1.7.1",
)
bazel_dep(name = "librarian", version = "")
local_path_override(
    module_name = "librarian",
    path = "../utils/librarian",
)

librarian_extension = use_extension("@librarian//:librarian.bzl", "librarian_extension")
librarian_extension.book(
    name = "the_great_gatsby",
    edition = "1995.12",
)

# The root module will get hamlet@1800.1 because hamlet@2005.1 is only a dev dependency of lib_a.
librarian_extension.book(
    name = "hamlet",
    edition = "1800.1",
)
use_repo(librarian_extension, "the_great_gatsby")
use_repo(librarian_extension, "hamlet")
