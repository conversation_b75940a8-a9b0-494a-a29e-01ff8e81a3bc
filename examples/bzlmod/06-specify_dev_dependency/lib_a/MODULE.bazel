module(
    name = "lib_a",
    version = "0.0.1",
)

# This won't force the root module to use bazel_skylib 1.2.0 because it's a dev dependency.
bazel_dep(
    name = "bazel_skylib",
    version = "1.7.1",
    dev_dependency = True,
)

bazel_dep(name = "librarian", version = "")

librarian_extension = use_extension("@librarian//:librarian.bzl", "librarian_extension")
librarian_extension.book(
    name = "the_great_gatsby",
    edition = "2003.7",
)
use_repo(librarian_extension, "the_great_gatsby")

# All tags belonging to librarian_extension_dev won't take effect if this module is used as a dependency.
librarian_extension_dev = use_extension("@librarian//:librarian.bzl", "librarian_extension", dev_dependency = True)
librarian_extension_dev.book(
    name = "hamlet",
    edition = "2005.1",
)
use_repo(librarian_extension_dev, "hamlet")
