# Load general dependencies
load("//:deps.bzl", "deps")

deps()

# Define dev dependencies directly in the WORKSPACE file.
load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")

http_archive(
    name = "bazel_skylib",
    sha256 = "bc283cdfcd526a52c3201279cda4bc298652efa898b10b4db0837dc51652756f",
    urls = ["https://github.com/bazelbuild/bazel-skylib/releases/download/1.7.1/bazel-skylib-1.7.1.tar.gz"],
)

load("@librarian//:librarian.bzl", "fetch_book")

fetch_book(
    name = "hamlet",
    edition = "2005.1",
)
