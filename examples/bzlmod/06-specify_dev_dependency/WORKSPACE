load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")

local_repository(
    name = "librarian",
    path = "../utils/librarian",
)

load("@librarian//:librarian.bzl", "fetch_book")

fetch_book(
    name = "the_great_gatsby",
    edition = "1995.12",
)

fetch_book(
    name = "hamlet",
    edition = "1800.1",
)

local_repository(
    name = "lib_a",
    path = "./lib_a",
)

load("@lib_a//:deps.bzl", "deps")

deps()

http_archive(
    name = "bazel_skylib",
    sha256 = "bc283cdfcd526a52c3201279cda4bc298652efa898b10b4db0837dc51652756f",
    urls = ["https://github.com/bazelbuild/bazel-skylib/releases/download/1.7.1/bazel-skylib-1.7.1.tar.gz"],
)
