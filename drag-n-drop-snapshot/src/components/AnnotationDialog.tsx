import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import type { Annotation } from "@/pages/Index";

interface Props {
  open: boolean;
  onClose: () => void;
  onSave: (type: string, remark: string) => void;
  initialData?: Annotation | null;
}

const ANNOTATION_TYPES = [
  "Variable: Alphabet",
  "Variable: Numeric",
  "Graphic",
  "Others",
];

export const AnnotationDialog = ({ open, onClose, onSave, initialData }: Props) => {
  const [type, setType] = useState(ANNOTATION_TYPES[0]);
  const [remark, setRemark] = useState("");

  useEffect(() => {
    if (initialData) {
      setType(initialData.type);
      setRemark(initialData.remark);
    } else {
      setType(ANNOTATION_TYPES[0]);
      setRemark("");
    }
  }, [initialData]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {initialData ? "Edit Annotation" : "New Annotation"}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Type</label>
            <Select value={type} onValueChange={setType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ANNOTATION_TYPES.map((t) => (
                  <SelectItem key={t} value={t}>
                    {t}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Remark</label>
            <Input
              value={remark}
              onChange={(e) => setRemark(e.target.value)}
              placeholder="Enter a remark..."
            />
          </div>
        </div>
        <div className="flex justify-end gap-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={() => onSave(type, remark)}>Save</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};