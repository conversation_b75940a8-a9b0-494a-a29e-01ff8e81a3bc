import { useEffect, useRef, useState } from "react";
import type { Annotation } from "@/pages/Index";

interface Props {
  imageUrl: string;
  annotations: Annotation[];
  onNewAnnotation: (rect: any) => void;
}

export const AnnotationCanvas = ({ imageUrl, annotations, onNewAnnotation }: Props) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [selectedAnnotation, setSelectedAnnotation] = useState<number | null>(null);
  const [ctx, setCtx] = useState<CanvasRenderingContext2D | null>(null);
  const RESIZE_HANDLE_SIZE = 8;

  // Initialize canvas and load image
  useEffect(() => {
    if (!canvasRef.current) return;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    if (!context) return;
    setCtx(context);

    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      context.drawImage(img, 0, 0);
      drawAnnotations();
    };
  }, [imageUrl]);

  // Draw all annotations
  const drawAnnotations = () => {
    if (!ctx || !canvasRef.current) return;
    const canvas = canvasRef.current;
    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      // Clear and redraw image
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);

      // Draw all annotations
      annotations.forEach((annotation, index) => {
        // Draw rectangle
        ctx.strokeStyle = selectedAnnotation === annotation.id ? '#4ade80' : '#86efac';
        ctx.fillStyle = 'rgba(134, 239, 172, 0.3)';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.rect(annotation.left, annotation.top, annotation.width, annotation.height);
        ctx.stroke();
        ctx.fill();

        // Draw resize handles if selected
        if (selectedAnnotation === annotation.id) {
          drawResizeHandles(annotation);
        }

        // Draw circle
        const centerX = annotation.left + annotation.width / 2;
        const centerY = annotation.top + annotation.height / 2;
        ctx.beginPath();
        ctx.arc(centerX, centerY, 10, 0, 2 * Math.PI);
        ctx.fillStyle = '#ef4444';
        ctx.fill();

        // Draw number
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(annotation.id.toString(), centerX, centerY);
      });
    };
  };

  const drawResizeHandles = (annotation: Annotation) => {
    if (!ctx) return;
    ctx.fillStyle = '#4ade80';
    // Draw corner handles
    const corners = [
      { x: annotation.left, y: annotation.top },
      { x: annotation.left + annotation.width, y: annotation.top },
      { x: annotation.left, y: annotation.top + annotation.height },
      { x: annotation.left + annotation.width, y: annotation.top + annotation.height }
    ];
    corners.forEach(corner => {
      ctx.fillRect(
        corner.x - RESIZE_HANDLE_SIZE / 2,
        corner.y - RESIZE_HANDLE_SIZE / 2,
        RESIZE_HANDLE_SIZE,
        RESIZE_HANDLE_SIZE
      );
    });
  };

  const isInResizeHandle = (x: number, y: number, annotation: Annotation) => {
    const corners = [
      { x: annotation.left, y: annotation.top },
      { x: annotation.left + annotation.width, y: annotation.top },
      { x: annotation.left, y: annotation.top + annotation.height },
      { x: annotation.left + annotation.width, y: annotation.top + annotation.height }
    ];
    return corners.some(corner => 
      x >= corner.x - RESIZE_HANDLE_SIZE / 2 &&
      x <= corner.x + RESIZE_HANDLE_SIZE / 2 &&
      y >= corner.y - RESIZE_HANDLE_SIZE / 2 &&
      y <= corner.y + RESIZE_HANDLE_SIZE / 2
    );
  };

  const isInAnnotation = (x: number, y: number, annotation: Annotation) => {
    return (
      x >= annotation.left &&
      x <= annotation.left + annotation.width &&
      y >= annotation.top &&
      y <= annotation.top + annotation.height
    );
  };

  useEffect(() => {
    drawAnnotations();
  }, [annotations, selectedAnnotation]);

  // Mouse event handlers
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Check if clicking on existing annotation
    const clickedAnnotation = annotations.find(annotation => {
      if (selectedAnnotation === annotation.id && isInResizeHandle(x, y, annotation)) {
        setIsResizing(true);
        return true;
      }
      if (isInAnnotation(x, y, annotation)) {
        setIsDragging(true);
        return true;
      }
      return false;
    });

    if (clickedAnnotation) {
      setSelectedAnnotation(clickedAnnotation.id);
    } else {
      setSelectedAnnotation(null);
      setIsDrawing(true);
    }

    setStartPos({ x, y });
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !ctx) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;
    
    if (isDrawing) {
      drawAnnotations();
      
      // Draw current rectangle
      ctx.strokeStyle = '#86efac';
      ctx.fillStyle = 'rgba(134, 239, 172, 0.3)';
      ctx.lineWidth = 2;
      ctx.beginPath();
      const width = currentX - startPos.x;
      const height = currentY - startPos.y;
      ctx.rect(startPos.x, startPos.y, width, height);
      ctx.stroke();
      ctx.fill();
    } else if (isDragging && selectedAnnotation !== null) {
      const deltaX = currentX - startPos.x;
      const deltaY = currentY - startPos.y;
      
      const updatedAnnotations = annotations.map(annotation => {
        if (annotation.id === selectedAnnotation) {
          return {
            ...annotation,
            left: annotation.left + deltaX,
            top: annotation.top + deltaY
          };
        }
        return annotation;
      });
      
      annotations.splice(0, annotations.length, ...updatedAnnotations);
      setStartPos({ x: currentX, y: currentY });
      drawAnnotations();
    } else if (isResizing && selectedAnnotation !== null) {
      const annotation = annotations.find(a => a.id === selectedAnnotation);
      if (annotation) {
        const deltaX = currentX - startPos.x;
        const deltaY = currentY - startPos.y;
        
        const updatedAnnotations = annotations.map(a => {
          if (a.id === selectedAnnotation) {
            return {
              ...a,
              width: Math.max(10, a.width + deltaX),
              height: Math.max(10, a.height + deltaY)
            };
          }
          return a;
        });
        
        annotations.splice(0, annotations.length, ...updatedAnnotations);
        setStartPos({ x: currentX, y: currentY });
        drawAnnotations();
      }
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !canvasRef.current) {
      setIsDragging(false);
      setIsResizing(false);
      return;
    }
    
    const rect = canvasRef.current.getBoundingClientRect();
    const endX = e.clientX - rect.left;
    const endY = e.clientY - rect.top;
    
    const width = endX - startPos.x;
    const height = endY - startPos.y;
    
    if (Math.abs(width) > 5 && Math.abs(height) > 5) {
      onNewAnnotation({
        left: Math.min(startPos.x, endX),
        top: Math.min(startPos.y, endY),
        width: Math.abs(width),
        height: Math.abs(height)
      });
    }
    
    setIsDrawing(false);
  };

  return (
    <canvas
      ref={canvasRef}
      className="max-w-full h-auto border border-gray-200"
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    />
  );
};