import { Pen, X } from "lucide-react";
import type { Annotation } from "@/pages/Index";

interface Props {
  annotations: Annotation[];
  onEdit: (annotation: Annotation) => void;
  onDelete: (id: number) => void;
}

export const AnnotationSidebar = ({ annotations, onEdit, onDelete }: Props) => {
  return (
    <div className="w-64 bg-white border-l border-gray-200 p-4 overflow-y-auto">
      <h2 className="text-lg font-semibold mb-4">Annotations</h2>
      <div className="space-y-3">
        {annotations.map((annotation) => (
          <div
            key={annotation.id}
            className="flex items-start p-3 bg-gray-50 rounded-lg"
          >
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="w-6 h-6 flex items-center justify-center bg-red-500 text-white rounded-full text-sm">
                  {annotation.id}
                </span>
                <span className="text-sm font-medium">{annotation.type}</span>
              </div>
              <p className="mt-1 text-sm text-gray-600">{annotation.remark}</p>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => onEdit(annotation)}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <Pen className="w-4 h-4" />
              </button>
              <button
                onClick={() => {
                  if (confirm("Are you sure you want to remove this annotation?")) {
                    onDelete(annotation.id);
                  }
                }}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};