import { useState } from "react";
import { ImageUploader } from "@/components/ImageUploader";
import { AnnotationCanvas } from "@/components/AnnotationCanvas";
import { AnnotationSidebar } from "@/components/AnnotationSidebar";
import { AnnotationDialog } from "@/components/AnnotationDialog";

export type Annotation = {
  id: number;
  type: string;
  remark: string;
  left: number;
  top: number;
  width: number;
  height: number;
};

const Index = () => {
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [selectedAnnotation, setSelectedAnnotation] = useState<Annotation | null>(null);
  const [showDialog, setShowDialog] = useState(false);
  const [tempRect, setTempRect] = useState<any>(null);

  const handleImageUpload = (imageUrl: string) => {
    setUploadedImage(imageUrl);
  };

  const handleNewAnnotation = (rect: any) => {
    setTempRect(rect);
    setShowDialog(true);
  };

  const handleAnnotationSave = (type: string, remark: string) => {
    if (tempRect) {
      const newAnnotation: Annotation = {
        id: annotations.length + 1,
        type,
        remark,
        left: tempRect.left,
        top: tempRect.top,
        width: tempRect.width,
        height: tempRect.height,
      };
      setAnnotations([...annotations, newAnnotation]);
      setShowDialog(false);
      setTempRect(null);
    }
  };

  const handleAnnotationDelete = (id: number) => {
    setAnnotations(annotations.filter(a => a.id !== id));
  };

  const handleAnnotationEdit = (annotation: Annotation) => {
    setSelectedAnnotation(annotation);
    setShowDialog(true);
  };

  const handleAnnotationUpdate = (type: string, remark: string) => {
    if (selectedAnnotation) {
      const updatedAnnotations = annotations.map(a =>
        a.id === selectedAnnotation.id ? { ...a, type, remark } : a
      );
      setAnnotations(updatedAnnotations);
      setShowDialog(false);
      setSelectedAnnotation(null);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <div className="flex-1 p-4">
        {!uploadedImage ? (
          <ImageUploader onUpload={handleImageUpload} />
        ) : (
          <AnnotationCanvas
            imageUrl={uploadedImage}
            annotations={annotations}
            onNewAnnotation={handleNewAnnotation}
          />
        )}
      </div>
      <AnnotationSidebar
        annotations={annotations}
        onEdit={handleAnnotationEdit}
        onDelete={handleAnnotationDelete}
      />
      <AnnotationDialog
        open={showDialog}
        onClose={() => {
          setShowDialog(false);
          setSelectedAnnotation(null);
          setTempRect(null);
        }}
        onSave={selectedAnnotation ? handleAnnotationUpdate : handleAnnotationSave}
        initialData={selectedAnnotation}
      />
    </div>
  );
};

export default Index;