#pragma once
#include <cstdint>
#include <format>
#include <iostream>
#include <unordered_map>
struct QtyNotional
{
    int openQty{0};
    int fillQty{0};
    double openNV{0.0};
    double fillNV{0.0};
    void Print() const
    {
        // std::cout << std::format("Open {} {}", openQty, openNV);
        std::cout << "open qty:" << openQty << ", nv:" << openNV << "\n";
        std::cout << "fill qty:" << fillQty << ", nv:" << fillNV << "\n";
    }
};
struct TotalQtyNotional
{
    QtyNotional sellData;
    QtyNotional buyData;
    void Print() const
    {
        std::cout << "Sell:\n";
        sellData.Print();
        std::cout << "Buy:\n";
        buyData.Print();
    }
};

struct TurnoverContext
{
    TotalQtyNotional &get(const std::string &symbol)
    {
        return _map[symbol];
    }
    void Print() const
    {
        std::cout << "TurnoverContext size:" << _map.size() << "\n";
        for (auto [x, y] : _map)
        {
            std::cout << "Symbol:" << x << "\n";
            y.Print();
        }
    }
    std::unordered_map<std::string, TotalQtyNotional> _map;
};
