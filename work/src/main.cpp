#include "TurnoverContext.h"
#include <iostream>


int main()
{
    TurnoverContext x;
    std::cout << "before:" <<sizeof(x) << "\n";
    x.Print();
    {
        auto& c = x.get("abc");
        c.buyData.openQty += 100;
        c.buyData.openNV += 1200;
    }

    {
        auto& c = x.get("def");
        c.sellData.openQty += 100;
        c.sellData.openNV += 1200;
        
    }
    
    x.Print();

    std::cout << "after:" << sizeof(x) << "\n";
    return 0;
}