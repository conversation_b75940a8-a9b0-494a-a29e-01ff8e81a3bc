cc_library(
    name = "lib",
    hdrs = glob(["lib/*.h"]),
    includes = ["lib"],
    copts = ["-std=c++20"],
    visibility = ["//visibility:public"]
)

cc_binary(
    name = "main",
    srcs = ["src/main.cpp"],
    deps = [":lib"]
)

cc_test(
    name = "test",
    srcs = ["test/test.cpp"],
    copts = ["-std=c++20"],
    
    deps = [
        "@googletest//:gtest",
        "@googletest//:gtest_main",
        ":lib"
    ]
)